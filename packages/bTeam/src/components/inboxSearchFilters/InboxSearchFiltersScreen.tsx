import React, { useCallback, useRef } from "react";
import {
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  StyleSheet,
  TextInputChangeEventData,
  View,
} from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  Button,
  CustomText,
  FONT_SIZES,
  SPACING,
  Theme,
  useThemeAwareObject,
  AdvancedSearchButtons,
  SearchCriteriaField,
  SearchCriteriaCheckboxField,
  CustomBottomSheet,
} from "b-ui-lib";
import { TEST_IDS } from "../../constants/testIds";
import {
  SEARCH_FIELD_NAMES,
  SEARCH_FIELD_NAMES_OPTIONS,
} from "../../constants/searchFieldsMessage";
import { InboxSearchFields } from "../../containers/InboxSearchFiltersHOC";

export type CheckboxOption = {
  name: string;
  value: string;
};

type Props = {
  setInboxSearchFields: (fields: {}) => void;
  clearInboxSearchFields: () => void;
  fetchGridMessages: () => void;
  navigation: NavigationProp<ParamListBase>;
  appliedSearchFiltersCount: number;
  setIsQuickSearchEnabledAction: (isEnabled: boolean) => void;
  selectedFieldName: string;
  setSelectedFieldName: (fieldName: string) => void;
  temporaryFilters: InboxSearchFields;
  setTemporaryFilters: (filters: InboxSearchFields) => void;
  keywordSuggestions: string[];
  recipientEmailSuggestions: string[];
};

const InboxSearchFiltersScreen: React.FC = ({
  setInboxSearchFields,
  clearInboxSearchFields,
  fetchGridMessages,
  navigation,
  appliedSearchFiltersCount,
  setIsQuickSearchEnabledAction,
  selectedFieldName,
  setSelectedFieldName,
  temporaryFilters,
  setTemporaryFilters,
  keywordSuggestions,
  recipientEmailSuggestions,
}: Props) => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const { styles, color } = useThemeAwareObject(createStyles);

  const handlePresentModalPress = useCallback(() => {
    bottomSheetRef.current?.expand();
  }, []);

  const handleDismiss = useCallback(() => {
    setSelectedFieldName("");
  }, []);

  const handleBackPress = () => {
    if (selectedFieldName) {
      bottomSheetRef.current?.close();
      return true;
    }

    return false;
  };

  const handleFieldPress = (fieldName: string) => {
    setSelectedFieldName(fieldName);
    handlePresentModalPress();
  };

  const handleOptionPress = (option: string) => {
    setTemporaryFilters({ ...temporaryFilters, [selectedFieldName]: option });
    bottomSheetRef.current?.close();
  };

  const handleApplyCriteriaPress = () => {
    setInboxSearchFields(temporaryFilters);
    setIsQuickSearchEnabledAction(false);
    fetchGridMessages();
    navigation.goBack();
  };

  const findOptionName = (field: string, optionValue: string) =>
    SEARCH_FIELD_NAMES_OPTIONS[field].find(
      (option) => option.value === optionValue
    )?.name;

  const toggleString = (base: string, value: string) => {
    let items = base ? base.trim().split(",") : [];

    if (items.includes(value)) {
      items = items.filter((item) => item !== value);
    } else {
      items.push(value);
    }

    return items.join(",");
  };

  const handleKeywordSuggestionPress = (suggestion: string) => {
    setTemporaryFilters({
      ...temporaryFilters,
      [SEARCH_FIELD_NAMES.searchText]: suggestion,
    });
  };

  const handleRecipientEmailSuggestionPress = (suggestion: string) => {
    setTemporaryFilters({
      ...temporaryFilters,
      [SEARCH_FIELD_NAMES.to]: suggestion,
    });
  };

  const FIELDS = [
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsKeywords,
      name: SEARCH_FIELD_NAMES.searchText,
      placeholder: "Add keywords",
      label: "Keywords",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      onChange: (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [SEARCH_FIELD_NAMES.searchText]: e?.nativeEvent?.text,
        });
      },
      value: temporaryFilters?.searchText,
      component: SearchCriteriaField,
      suggestions: keywordSuggestions,
      handleSuggestionPress: handleKeywordSuggestionPress,
    },
    {
      name: SEARCH_FIELD_NAMES.searchIn,
      label: "Search in",
      handleCheckboxPress: (checkboxOption: CheckboxOption) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [SEARCH_FIELD_NAMES.searchIn]: toggleString(
            temporaryFilters?.searchIn,
            checkboxOption.value
          ),
        });
      },
      value: temporaryFilters?.searchIn,
      component: SearchCriteriaCheckboxField,
      checkboxOptions: SEARCH_FIELD_NAMES_OPTIONS[SEARCH_FIELD_NAMES.searchIn],
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsInOut,
      name: SEARCH_FIELD_NAMES.inOut,
      placeholder: "Not Set",
      label: "In / Out",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => handleFieldPress(SEARCH_FIELD_NAMES.inOut),
      value: findOptionName(SEARCH_FIELD_NAMES.inOut, temporaryFilters?.inOut),
      component: SearchCriteriaField,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsFrom,
      name: SEARCH_FIELD_NAMES.from,
      placeholder: "Sender email address",
      label: "From",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      onChange: (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [SEARCH_FIELD_NAMES.from]: e?.nativeEvent?.text,
        });
      },
      value: temporaryFilters?.from,
      keyboardType: "email-address",
      autoComplete: "email",
      textContentType: "emailAddress",
      component: SearchCriteriaField,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsTo,
      name: SEARCH_FIELD_NAMES.to,
      placeholder: "Recipient email address",
      label: "To",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      onChange: (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [SEARCH_FIELD_NAMES.to]: e?.nativeEvent?.text,
        });
      },
      value: temporaryFilters?.to,
      keyboardType: "email-address",
      autoComplete: "email",
      textContentType: "emailAddress",
      component: SearchCriteriaField,
      suggestions: recipientEmailSuggestions,
      handleSuggestionPress: handleRecipientEmailSuggestionPress,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsDatePeriod,
      name: SEARCH_FIELD_NAMES.datePeriod,
      placeholder: "Not Set",
      label: "Date Period",
      iconName: "calendar",
      iconColor: color.SEARCH_FIELD_CALENDAR_ICON,
      editable: false,
      onPress: () => handleFieldPress(SEARCH_FIELD_NAMES.datePeriod),
      value: findOptionName(
        SEARCH_FIELD_NAMES.datePeriod,
        temporaryFilters?.datePeriod
      ),
      component: SearchCriteriaField,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsFlagged,
      name: SEARCH_FIELD_NAMES.flagged,
      placeholder: "Not Set",
      label: "Flagged",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => handleFieldPress(SEARCH_FIELD_NAMES.flagged),
      value: findOptionName(
        SEARCH_FIELD_NAMES.flagged,
        temporaryFilters?.flagged
      ),
      component: SearchCriteriaField,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsHasAttachments,
      name: SEARCH_FIELD_NAMES.hasAttachments,
      placeholder: "Not Set",
      label: "Has Attachments",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => handleFieldPress(SEARCH_FIELD_NAMES.hasAttachments),
      value: findOptionName(
        SEARCH_FIELD_NAMES.hasAttachments,
        temporaryFilters?.hasAttachments
      ),
      component: SearchCriteriaField,
    },
    {
      testID: TEST_IDS.inboxSearchFiltersFieldsReadUnread,
      name: SEARCH_FIELD_NAMES.readUnread,
      placeholder: "Not Set",
      label: "Read / Unread",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => handleFieldPress(SEARCH_FIELD_NAMES.readUnread),
      value: findOptionName(
        SEARCH_FIELD_NAMES.readUnread,
        temporaryFilters?.readUnread
      ),
      component: SearchCriteriaField,
    },
  ];

  const clearAll = () => {
    clearInboxSearchFields();
  };

  return (
    <GestureHandlerRootView>
      <View style={styles.container}>
        <AdvancedSearchButtons
          isClearAllButtonVisible={appliedSearchFiltersCount > 0}
          handleClearAll={clearAll}
          isSearchCriteriaButtonActive
          style={{ buttonsContainer: styles.headerButtons }}
        />

        <View style={styles.inputsContainer}>
          <CustomText style={styles.advancedText}>Advanced Criteria</CustomText>

          <ScrollView contentContainerStyle={styles.scrollViewContentContainer}>
            {FIELDS?.map((field, index) => {
              const {
                testID,
                name,
                component: Component,
                ...restProps
              } = field || {};
              return (
                <Component
                  testID={testID}
                  key={`${name}  ${index}`}
                  {...restProps}
                />
              );
            })}
          </ScrollView>
        </View>

        <View style={styles.applyButtonContainer}>
          <Button
            testID={TEST_IDS.inboxSearchFiltersApplyButton}
            title="Apply Criteria"
            textStyle={styles.applyButtonText}
            onPress={handleApplyCriteriaPress}
          />
        </View>
      </View>

      <CustomBottomSheet
        bottomSheetRef={bottomSheetRef}
        handleBackPress={handleBackPress}
        hasBackdrop
        handleBackdropPress={handleDismiss}
      >
        <View style={styles.bottomSheetViewContainer}>
          {SEARCH_FIELD_NAMES_OPTIONS?.[selectedFieldName]?.map((option) => (
            <Pressable
              testID={`bottomSheetOption-${option?.name}`}
              key={`${option?.name}-${option.value}`}
              onPress={() => handleOptionPress(option?.value)}
            >
              <CustomText style={styles.bottomSheetOption}>
                {option?.name}
              </CustomText>
            </Pressable>
          ))}
        </View>
      </CustomBottomSheet>
    </GestureHandlerRootView>
  );
};

export default InboxSearchFiltersScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    avoidingViewContainer: {
      flex: 1,
      justifyContent: "center",
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    container: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    headerButtons: {
      padding: SPACING.M,
    },
    inputsContainer: {
      flex: 1,
      paddingTop: SPACING.XL,
      paddingHorizontal: SPACING.M,
    },
    advancedText: {
      color: color.TEXT_SEARCH_INVERTED,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "bold",
      alignSelf: "flex-start",
      marginBottom: SPACING.M,
    },
    scrollViewContentContainer: {
      gap: SPACING.SIX,
    },
    applyButtonContainer: {
      borderTopWidth: 1,
      borderTopColor: color.SEARCH_FIELD_BORDER,
      padding: SPACING.M,
    },
    applyButtonText: {
      fontWeight: "500",
    },
    bottomSheetStyle: {
      backgroundColor: color.BACKGROUND,
    },
    bottomSheetIndicatorStyle: {
      backgroundColor: color.MESSAGE_FLAG,
    },
    bottomSheetViewContainer: {
      backgroundColor: color.BACKGROUND,
      flexDirection: "row",
      gap: SPACING.S,
      justifyContent: "center",
      flexWrap: "wrap",
      padding: SPACING.L,
    },
    bottomSheetOption: {
      padding: SPACING.M,
      borderRadius: 8,
      backgroundColor: color.BRAND_DEFAULT,
      color: color.TEXT_DEFAULT,
      textAlign: "center",
      minWidth: 70,
    },
  });

  return { styles, color };
};
