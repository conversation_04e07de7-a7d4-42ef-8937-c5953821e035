const filterSearchSuggestions = (
  searchSuggestions: string[],
  searchText: string
) => {
  if (
    !searchText ||
    searchText.trim() === "" ||
    !searchSuggestions ||
    searchSuggestions.length === 0
  ) {
    return [];
  }

  return searchSuggestions
    ?.filter((suggestion) => {
      const lowerCaseSuggestion = suggestion.toLowerCase();
      const newSearchText = searchText.toLowerCase();

      return (
        lowerCaseSuggestion.includes(newSearchText) &&
        lowerCaseSuggestion !== newSearchText
      );
    })
    .slice(0, 5);
};

export default filterSearchSuggestions;
