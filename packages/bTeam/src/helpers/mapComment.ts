import { calculateAvatarName } from "./calculateAvatarName";
import {
  NotificationDTO, NotificationMessageInfo,
} from "../types/DTOs/NotificationDTO";
import { CommentDTO } from "../types/DTOs/CommentDTO";

export const mapNotification = (comment: NotificationDTO) => {
  return {
    id: comment.CMM_Guid,
    parentId: comment.CMM_CMM_Guid,
    entityId: comment.CMM_ENT_Id,
    entityPkId: comment.CMM_EntityPK_Guid,
    description: comment.Description,
    avatarName: calculateAvatarName(comment.USR_UserName),
    username: comment.USR_UserName,
    isStarred: comment.CMU_Starred || false,
    date: comment.CMM_DateTime,
    attachmentCount: comment.CMM_AttachmentsCount || 0,
    countAll: comment?.countAll,
    isViewed: Boolean(comment?.CMU_TimeViewed),
    isNotified: comment?.Notified,
    dateTime: comment?.CMM_DateTime,
    title: comment?.Title,
  };
};

export const mapComment = (comment: CommentDTO) => {
  return {
    id: comment?.CMM_Guid,
    parentId: comment?.CMM_CMM_Guid,
    entityId: comment?.CMM_ENT_Id,
    entityPkId: comment?.CMM_EntityPK_Guid,
    descriptionHtml: comment?.CMM_Description,
    avatarName: calculateAvatarName(comment?.CommentCreatedUserName),
    username: comment?.CommentCreatedUserName,
    isStarred: comment?.CMU_Starred || false,
    countAll: comment?.countAll,
    timeViewed: comment?.CMU_TimeViewed,
    isViewed: Boolean(comment?.CMU_TimeViewed),
    attachmentCount: comment?.CMM_AttachmentsCount || 0,
    dateTime: comment?.CMM_CreatedTimestamp,
    attachmentIds: !!comment?.AttachmentsMobileJson ? JSON.parse(comment.AttachmentsMobileJson)?.map((attch) => attch.FLN_Guid) :
      comment?.Attachments?.map((attch) => attch.FLN_Guid) || [],
    notifiedUsers: []
  };
};

export const mapNotificationMessageInfo = (notificationMessageInfo: NotificationMessageInfo) => {
  return {
    id: notificationMessageInfo?.MSG_Guid,
    inOut: notificationMessageInfo.MSG_InOut,
    avatarName: calculateAvatarName(notificationMessageInfo.USR_UserName),
    username: notificationMessageInfo.USR_UserName,
    from: notificationMessageInfo.MSG_From,
    tos: notificationMessageInfo?.Tos,
    ccs: notificationMessageInfo?.Ccs,
    bccs: notificationMessageInfo?.Bccs,
  };
}
