import { FolderDTO } from "../types/DTOs/FolderDTO";
import { Folder } from "../types/folder";

const IS_SEARCH_ITEM = 3;

export const mapFolder = (folder: FolderDTO, oldFolder: Folder): Folder => ({
  id: folder.FLD_Guid,
  parentFolderId: folder.FLD_FLD_Guid || null,
  name: folder.FLD_Name,
  level: folder.FLD_Level || null,
  sortOrder: folder.FLD_SortOrder || null,
  isVisible: folder.FLD_IsVisible || null,
  createdBy: folder.FLD_CreatedUserGuid,
  createdAt: new Date(folder.FLD_CreatedTimestamp),
  updatedBy: folder.FLD_UpdatedUserGuid,
  updatedAt: new Date(folder.FLD_UpdatedTimestamp),
  deletedBy: folder.FLD_DeletedUserGuid || null,
  status: folder.RecordStatus,
  isExpanded: false,
  isSearchItem: folder.FLD_FLT_Id === IS_SEARCH_ITEM,
  isLoading: false,
  emailsCount: folder.FLD_Count || null,
  emailIds: oldFolder?.emailIds ? oldFolder.emailIds : [],
  hasChildren: folder.HasChildren || false,
  childrenIds: oldFolder?.childrenIds ? oldFolder.childrenIds : [],
  areChildrenFetched: false,
});
