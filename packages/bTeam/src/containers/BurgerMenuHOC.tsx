import React, { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchGridMessages,
  getFolderChildren,
  setSelectedFolder,
  toggleFolderExpand,
} from "../slices/gridMessageSlice";
import { cancelMultiSelection } from "../slices/generalSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";

// Components
import BurgerMenuScreen from "../components/burgerMenu/BurgerMenuScreen";
import { createChildrenHierarchy } from "../helpers/createChildrenHierarchy";
import { SCREEN_NAMES } from "../constants/screenNames";
import {
  INBOX_SEARCH_FIELD_INITIAL_VALUE,
  SEARCH_FIELD_NAMES,
} from "../constants/searchFieldsMessage";
import {
  clearSearchFields,
  setSearchFilters,
} from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";

type Props = {};

const BurgerMenuHOC: React.FC = ({}: Props) => {
  const [isBurgerMenuOpen, setIsBurgerMenuOpen] = useState<boolean>(false);

  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { folders, selectedFolderId } = useSelector(
    (state) => state.persist.gridMessageSlice
  );
  const { userName } = useSelector((state) => state.persist.bTeamAuth);

  const fetchGridMessagesAction = () => dispatch(fetchGridMessages());

  const setInboxSearchFieldsAction = (fields: {}) => {
    dispatch(
      setSearchFilters({
        entityType: SEARCH_ENTITIES.messages,
        filters: fields,
      })
    );
  };

  const clearInboxSearchFieldsAction = () => {
    dispatch(clearSearchFields({ entityType: SEARCH_ENTITIES.messages }));
  };

  const openBurgerMenu = () => setIsBurgerMenuOpen(true);
  const closeBurgerMenu = () => setIsBurgerMenuOpen(false);

  const handleToggleFolderExpand = (folderId: string) => {
    if (
      !folders.byId?.[folderId].isExpanded &&
      !folders.byId?.[folderId]?.areChildrenFetched
    ) {
      dispatch(getFolderChildren({ folderId }));
    }

    dispatch(toggleFolderExpand(folderId));
  };

  const foldersHierarchy = useMemo(
    () => createChildrenHierarchy(folders.byId, folders.rootIds, userName),
    [folders]
  );

  const onSelectFolder = (id) => {
    dispatch(setSelectedFolder(id));
    dispatch(cancelMultiSelection());
    clearSearchFields({ entityType: SEARCH_ENTITIES.messages });
    clearInboxSearchFieldsAction();
    closeBurgerMenu();
  };

  const handleFolderPressSearchItem = (folderId: string) => {
    const selectedSearchItem = folders.byId?.[folderId];

    setInboxSearchFieldsAction({
      ...INBOX_SEARCH_FIELD_INITIAL_VALUE,
      [SEARCH_FIELD_NAMES.searchText]: selectedSearchItem.name,
    });

    fetchGridMessagesAction();
    dispatch(cancelMultiSelection());
    closeBurgerMenu();

    navigation.navigate(SCREEN_NAMES.inboxStack);
  };

  return (
    <BurgerMenuScreen
      isBurgerMenuOpen={isBurgerMenuOpen}
      openBurgerMenu={openBurgerMenu}
      closeBurgerMenu={closeBurgerMenu}
      folders={foldersHierarchy}
      handleToggleFolderExpand={handleToggleFolderExpand}
      onSelectFolder={onSelectFolder}
      selectedFolderId={selectedFolderId}
      handleFolderPressSearchItem={handleFolderPressSearchItem}
    />
  );
};

export default BurgerMenuHOC;
