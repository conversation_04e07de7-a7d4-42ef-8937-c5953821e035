import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { fetchGridMessages } from "../slices/gridMessageSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";
import {
  clearSearchFields,
  setIsQuickSearchEnabled,
  setSearchFilters,
} from "../slices/searchFiltersSlice";

// Components
import InboxSearchFiltersScreen from "../components/inboxSearchFilters/InboxSearchFiltersScreen";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import { INBOX_SEARCH_FIELD_INITIAL_VALUE } from "../constants/searchFieldsMessage";

export type InboxSearchFields = {
  inOut: string;
  searchText: string;
  searchIn: string;
  from: string;
  to: string;
  datePeriod: string;
  flagged: string;
  hasAttachments: string;
  readUnread: string;
};

type Props = {};

const InboxSearchFiltersHOC: React.FC = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();

  const { appliedSearchFiltersCount, searchFilters } = useSelector(
    (state) => state.root.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.messages]
  );

  const { searchSuggestions } = useSelector(
    (state) =>
      state.persist.bTeamSearchSuggestionsSlice?.[SEARCH_ENTITIES.messages]
  );

  const { userEmailAdresses } = useSelector(
    (state: any) => state.persist.bTeamUsers
  );

  const [selectedFieldName, setSelectedFieldName] = useState<string>("");
  const [temporaryFilters, setTemporaryFilters] = useState<InboxSearchFields>(
    searchFilters && Object.values(searchFilters)?.length > 0
      ? searchFilters
      : INBOX_SEARCH_FIELD_INITIAL_VALUE
  );

  const setInboxSearchFieldsAction = (fields: {}) => {
    dispatch(
      setSearchFilters({
        entityType: SEARCH_ENTITIES.messages,
        filters: fields,
      })
    );
  };

  const clearInboxSearchFieldsAction = () => {
    clearSearchFields({ entityType: SEARCH_ENTITIES.messages });
  };

  const fetchGridMessagesAction = () => dispatch(fetchGridMessages());

  const setIsQuickSearchEnabledAction = (isEnabled: boolean) => {
    dispatch(
      setIsQuickSearchEnabled({
        entityType: SEARCH_ENTITIES.messages,
        isEnabled,
      })
    );
  };

  const keywordSuggestions = useMemo(() => {
    if (!searchSuggestions || searchSuggestions.length === 0) {
      return [];
    }

    // If no search text, return first three suggestions
    if (!temporaryFilters?.searchText || temporaryFilters.searchText.trim() === '') {
      return searchSuggestions.slice(0, 3);
    }

    const searchTerm = temporaryFilters.searchText.toLowerCase();

    return searchSuggestions.reduce((matches, suggestion) => {
      // Stop once we have 3 matches
      if (matches.length >= 3) return matches;

      const lowerSuggestion = suggestion.toLowerCase();
      // Add if it includes searchTerm but isn't exactly the same
      if (lowerSuggestion.includes(searchTerm) && lowerSuggestion !== searchTerm) {
        matches.push(suggestion);
      }

      return matches;
    }, []);
  }, [temporaryFilters?.searchText, searchSuggestions]);

  const recipientEmailSuggestions = useMemo(() => {
    if (
      !temporaryFilters?.to ||
      temporaryFilters.to.trim() === "" ||
      !userEmailAdresses?.allIds
    ) {
      return [];
    }

    const searchTerm = temporaryFilters.to.toLowerCase();

    return userEmailAdresses.allIds.reduce((matches, id) => {
      // Stop once we have 3 matches
      if (matches.length >= 3) return matches;

      const user = userEmailAdresses.byId[id];
      if (
        user?.email &&
        user.email.toLowerCase().includes(searchTerm) &&
        user.email.toLowerCase() !== searchTerm // Don't add exact matches
      ) {
        matches.push(user.email);
      }
      return matches;
    }, []);
  }, [temporaryFilters?.to, userEmailAdresses]);

  return (
    <View style={styles.container}>
      <InboxSearchFiltersScreen
        setInboxSearchFields={setInboxSearchFieldsAction}
        clearInboxSearchFields={clearInboxSearchFieldsAction}
        fetchGridMessages={fetchGridMessagesAction}
        navigation={navigation}
        appliedSearchFiltersCount={appliedSearchFiltersCount}
        setIsQuickSearchEnabledAction={setIsQuickSearchEnabledAction}
        selectedFieldName={selectedFieldName}
        setSelectedFieldName={setSelectedFieldName}
        temporaryFilters={temporaryFilters}
        setTemporaryFilters={setTemporaryFilters}
        keywordSuggestions={keywordSuggestions}
        recipientEmailSuggestions={recipientEmailSuggestions}
      />
    </View>
  );
};

export default InboxSearchFiltersHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
