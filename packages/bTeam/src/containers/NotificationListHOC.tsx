import React, { useEffect, useRef, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import BottomSheet from "@gorhom/bottom-sheet";
import {
  clearOffset,
  fetchGridNotifications,
} from "../slices/notificationsSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import Toast from "react-native-toast-message";
import {
  cancelMultiSelection,
  clearMarkNotificationAsReadUnReadMessages,
  clearNotificationsSearchFields,
  deselectNotification,
  fetchParticipantUsers,
  longTapToSelectNotification,
  markNotificationsAsReadUnRead,
  setIsQuickSearchEnabled,
  setSearchFilters,
  starNotification,
  tapToSelectAdditionalNotification,
  unSelectAllNotifications,
} from "../slices/generalNotificationsSlice";
import { SCREEN_NAMES } from "../constants/screenNames";

// Components
import NotificationListScreen from "../components/notificationList/NotificationListScreen";
import { type Theme, CustomText, useThemeAwareObject } from "b-ui-lib";
import { ActivityIndicator, StyleSheet, View } from "react-native";
import { addSearchSuggestion } from "../slices/searchSuggestionsSlice";
import SearchBarAnimationHOC from "./SearchBarAnimationHOC";
import NotificationSearchControls from "../navigation/headers/NotificationSearchControls";

export const NOTIFICATIONS_INITIAL_PAGE_SIZE = 100;

const NotificationListHOC: React.FC = () => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const dispatch = useDispatch();

  const {
    notificationsAllIds,
    notificationsIsLoading,
    notificationsIsLoadingListMore,
    count,
    offset,
    notificationsErrorText,
  } = useSelector((state) => state.persist.bTeamNotificationSlice);

  const { comments } = useSelector((state) => state.persist.bTeamCommentsSlice);

  const {
    isMultiSelectActive,
    selectedNotificationIds,
    markNotificationsAsReadUnReadSuccessMessage,
    markNotificationsAsReadUnReadErrorMessage,
    filteredNotificationIds,
    searchFiltersCounter,
  } = useSelector((state) => state.root.bTeamGeneralNotificationSlice);

  const { color } = useThemeAwareObject(createStyles);

  const hasSearchFilteredResults = useMemo(
    () => searchFiltersCounter > 0,
    [searchFiltersCounter]
  );

  // We map from filteredNotificationIds when we have search criteria
  const notificationsArray = useMemo(() => {
    if (hasSearchFilteredResults) {
      return filteredNotificationIds.map(
        (notificationId: string) => comments.byId[notificationId]
      );
    }

    return notificationsAllIds.map(
      (notificationId: string) => comments.byId[notificationId]
    );
  }, [notificationsAllIds, filteredNotificationIds, comments]);

  const { searchSuggestions } = useSelector(
    (state) => state.persist.bTeamNotificationSlice
  );

  const { isQuickSearchEnabled, searchFilters } = useSelector(
    (state) => state.root.bTeamGeneralNotificationSlice
  );

  const clearNotificationsSearchFieldsAction = () => {
    dispatch(clearNotificationsSearchFields());
  };

  const setSearchFiltersAction = (payload: string) => {
    dispatch(setSearchFilters(payload));
  };

  const setIsQuickSearchEnabledAction = (payload: boolean) => {
    dispatch(setIsQuickSearchEnabled(payload));
  };

  const addSearchSuggestionAction = (payload: boolean) => {
    dispatch(addSearchSuggestion(payload));
  };

  const fetchGridNotificationsAction = (offset?: number) => {
    dispatch(fetchGridNotifications({ offset }));
  };

  const clearOffsetAction = () => {
    dispatch(clearOffset());
  };

  const longTapToSelectNotificationAction = (notificationId: string) => {
    dispatch(longTapToSelectNotification(notificationId));
  };

  const tapToSelectAdditionalNotificationAction = (notificationId: string) => {
    dispatch(tapToSelectAdditionalNotification(notificationId));
  };

  const deselectNotificationAction = (notificationId: string) => {
    dispatch(deselectNotification(notificationId));
  };

  const cancelMultiSelectionAction = () => {
    dispatch(cancelMultiSelection());
  };

  const unSelectAllNotificationsAction = () => {
    dispatch(unSelectAllNotifications());
  };

  const markNotificationsAsReadAction = (payload: {
    read: boolean;
    selectedNotificationIds: string[];
  }) => {
    dispatch(markNotificationsAsReadUnRead({ ...payload, showToast: true }));
  };

  const clearMarkNotificationAsReadUnReadMessagesAction = () => {
    dispatch(clearMarkNotificationAsReadUnReadMessages());
  };

  const starNotificationAction = (payload: {
    notificationId: string;
    isStarred: boolean;
  }) => {
    dispatch(starNotification(payload));
  };

  useEffect(() => {
    fetchGridNotificationsAction();
  }, []);

  useEffect(() => {
    return () => clearOffsetAction();
  }, []);

  // Show success/error toast
  useEffect(() => {
    if (
      !markNotificationsAsReadUnReadSuccessMessage &&
      !markNotificationsAsReadUnReadErrorMessage
    )
      return;

    if (
      markNotificationsAsReadUnReadSuccessMessage ||
      markNotificationsAsReadUnReadErrorMessage
    ) {
      Toast.show({
        type: markNotificationsAsReadUnReadSuccessMessage ? "success" : "error",
        text1: markNotificationsAsReadUnReadSuccessMessage
          ? markNotificationsAsReadUnReadSuccessMessage
          : markNotificationsAsReadUnReadErrorMessage,
        onPress() {
          Toast.hide();
        },
        onHide: clearMarkNotificationAsReadUnReadMessagesAction,
        position: "bottom",
      });
    }
  }, [
    markNotificationsAsReadUnReadSuccessMessage,
    markNotificationsAsReadUnReadErrorMessage,
  ]);

  // Close bottom sheet when multiSelect is not active
  useEffect(() => {
    if (
      !isMultiSelectActive &&
      isMultiSelectActive !== null &&
      isMultiSelectActive !== undefined
    ) {
      bottomSheetRef.current?.close();
    }
  }, [isMultiSelectActive, bottomSheetRef]);

  // Infinite scroll load more function
  const loadMoreNotifications = () => {
    if (
      !notificationsIsLoadingListMore && // Prevent new fetch during initial load
      notificationsAllIds.length < count // Ensure more data is available
    ) {
      const newOffset = offset + NOTIFICATIONS_INITIAL_PAGE_SIZE;

      fetchGridNotificationsAction(newOffset);
    }
  };

  const handleTapNotification = (notificationId: string) => {
    navigation.navigate(SCREEN_NAMES.notification, { notificationId });
  };

  const handleMarkNotificationsAsReadUnRead = (read: boolean) => {
    if (read) {
      return markNotificationsAsReadAction({ read, selectedNotificationIds });
    }

    return markNotificationsAsReadAction({ read, selectedNotificationIds });
  };

  return (
    <SearchBarAnimationHOC
      searchBar={
        <NotificationSearchControls
          searchText={searchFilters.searchText}
          searchSuggestions={searchSuggestions}
          searchFiltersCounter={searchFiltersCounter}
          isQuickSearchEnabled={isQuickSearchEnabled}
          setSearchFilters={setSearchFiltersAction}
          setIsQuickSearchEnabled={setIsQuickSearchEnabledAction}
          fetchGridNotifications={fetchGridNotificationsAction}
          addSearchSuggestion={addSearchSuggestionAction}
          clearNotificationsSearchFields={clearNotificationsSearchFieldsAction}
          navigateToFilters={() => {
            navigation.navigate(SCREEN_NAMES.notificationsSearchFilters);
          }}
        />
      }
    >
      <NotificationListScreen
        notifications={notificationsArray}
        handleRefreshList={fetchGridNotificationsAction}
        isLoading={notificationsIsLoading}
        loadMoreNotifications={loadMoreNotifications}
        handleStarPress={starNotificationAction}
        handleTapNotification={handleTapNotification}
        longTapToSelectNotification={longTapToSelectNotificationAction}
        tapToSelectAdditionalNotification={
          tapToSelectAdditionalNotificationAction
        }
        deselectNotification={deselectNotificationAction}
        cancelMultiSelection={cancelMultiSelectionAction}
        unSelectAllNotificationsAction={unSelectAllNotificationsAction}
        isMultiSelectActive={isMultiSelectActive}
        selectedNotificationIds={selectedNotificationIds}
        bottomSheetRef={bottomSheetRef}
        errorMessage={
          notificationsErrorText
            ? "Something went wrong:\n" + notificationsErrorText
            : ""
        }
        handleMarkNotificationsAsReadUnRead={
          handleMarkNotificationsAsReadUnRead
        }
        emptyMessage={
          hasSearchFilteredResults ? "No results found" : "List Empty"
        }
        listFooterComponent={
          notificationsAllIds.length < count ||
          notificationsIsLoadingListMore ? (
            <View style={{ padding: 10 }}>
              {notificationsIsLoadingListMore && (
                <ActivityIndicator size="small" color={color.MESSAGE_FLAG} />
              )}
            </View>
          ) : (
            <View style={{ padding: 10 }}>
              {notificationsAllIds > 0 && (
                <CustomText style={{ textAlign: "center" }}>
                  No more items to show
                </CustomText>
              )}
            </View>
          )
        }
      />
    </SearchBarAnimationHOC>
  );
};

export default NotificationListHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
  });

  return { styles, color };
};
