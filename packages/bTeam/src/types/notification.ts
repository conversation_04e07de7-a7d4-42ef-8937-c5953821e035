export type NotificationData = {
  id: string;
  parentId: string;
  title: string;
  description: string;
  descriptionHtml: string;
  entityId: number;
  entityPkId: string;
  avatarName: string;
  username: string;
  dateTime: string;
  isStarred: boolean;
  isViewed: boolean;
  countAll: number;
  isNotified: boolean;
  timeViewed: string;
  attachmentsCount: number;
  commentIds: string[];
};

export type NotificationMessageInfoData = {
  id: string;
  inOut: number;
  avatarName: string;
  username: string;
  from: string;
  tos: {
    DisplayName: string | null;
    EmailAddress: string;
  }[];
  ccs: {
    DisplayName: string | null;
    EmailAddress: string;
  }[];
  bccs: {
    DisplayName: string | null;
    EmailAddress: string;
  }[];
};
