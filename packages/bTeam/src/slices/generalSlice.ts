import { createSlice } from "@reduxjs/toolkit";
import { DraftMessageDTO } from "../types/DTOs/DraftMessageDTO";
import { RelatedUsersList } from "../types/userMails";
import {
  RelatedUsersDTO,
  RelatedUsersListDTO,
} from "../types/DTOs/MessageCommentUsersDTO";

// Initial state with Messages and Folders structures
const initialState = {
  selectedMessageIds: [], // IDs of selected messages for multi-select actions
  messageBodyTextFirstChars: "",
  bottomSheetMenuState: null,
  isMultiSelectActive: false, // Flag to indicate if multi-select mode is active
  isHeaderVisible: true, // Flag to control header visibility,
  sendMessageLoading: false as boolean,
  sendMessageError: "" as string,
  sendMessageSuccess: false as boolean,
  sendDraftMessageLoading: false,
  sendDraftMessageError: "",
  sendDraftMessageSuccess: false as boolean,
  getDraftMessageError: "",
  getDraftMessageLoading: false,
  draftMessage: {} as DraftMessageDTO,
  postMessageCommentLoading: false,
  postMessageCommentError: "",
  postMessageCommentSuccess: false,
  starMessageCommentLoading: false,
  starMessageCommentUsersError: "",
  getMessageCommentUsersLoading: false,
  getMessageCommentUsersError: "",
  uploadAttachmentLoading: false,
  uploadAttachmentError: "",
  commentMessageUsers: [] as RelatedUsersList,
};

// Updated message slice with types for state and reducers
const generalSlice = createSlice({
  name: "general",
  initialState,
  reducers: {
    clearFolders(state) {
      return state;
    },
    addFolders(state, action) {
      return state;
    },
    removeFolder(state, action) {
      return state;
    },
    setDefaultFolder(state, action) {
      return state;
    },
    archiveToFolder(state, action) {
      return state;
    },
    setFromSettings(state, action) {
      return state;
    },
    setFromArchiveMessage(state, action) {
      return state;
    },
    updateFolderMessages: (state, { payload }) => { },
    setBottomSheetMenuState(state, action) {
      state.bottomSheetMenuState = action.payload;
    },
    longTapToSelectEmail(state, action) {
      // Initialize multi-selection mode
      state.isMultiSelectActive = true;
      if (!state.selectedMessageIds.includes(action.payload)) {
        state.selectedMessageIds.push(action.payload);
      }
    },
    tapToSelectAdditionalEmail(state, action) {
      // Select additional email in multi-select mode
      if (!state.selectedMessageIds.includes(action.payload)) {
        state.selectedMessageIds.push(action.payload);
      }
    },
    deselectEmail(state, action) {
      // Remove email from selected list
      state.selectedMessageIds = state.selectedMessageIds.filter(
        (id) => id !== action.payload
      );
    },
    cancelMultiSelection(state) {
      state.selectedMessageIds = [];
      state.isMultiSelectActive = false;
    },
    openMoreActionsDropdown(state) {
      // Open additional actions dropdown (e.g., Mark as Read, Move, Link to Case)
    },
    linkEmailsToCase(state, action) {
      // Link selected emails to a case based on action.payload
    },
    selectAllEmails(state, { payload }) {
      // Select all emails currently visible on the screen
      state.selectedMessageIds = [...payload];
    },
    unSelectAllEmails(state) {
      // UnSelect all emails currently visible on the screen
      state.selectedMessageIds = [];
    },
    clearAllSelections(state) {
      // Clear all email selections
      state.selectedMessageIds = [];
    },
    scrollToHideHeader(state) {
      // Hide the top header on scroll down
      state.isHeaderVisible = false;
    },
    scrollToRevealHeader(state) {
      // Reveal the top header on scroll up
      state.isHeaderVisible = true;
    },
    loadMoreEmailsOnScroll(state) {
      // Placeholder for loading more emails when scrolling down
    },
    sendMessage: (state, { payload }) => {
      state.sendMessageLoading = true;
    },
    sendMessageSuccess: (state, { payload }) => {
      state.sendMessageLoading = false;
      state.sendMessageError = "";
      state.sendMessageSuccess = true;
    },
    sendMessageFailed: (state, { payload }) => {
      state.sendMessageLoading = false;
      state.sendMessageError = payload[0]?.response?.text;
      state.sendMessageSuccess = false;
    },
    sendMessageClearStatus: (state) => {
      state.sendMessageLoading = false;
      state.sendMessageError = "";
      state.sendMessageSuccess = false;
      state.sendDraftMessageLoading = false;
      state.sendDraftMessageError = "";
      state.sendDraftMessageSuccess = false;
    },
    getDraftMessage: (state, { payload }) => {
      state.getDraftMessageLoading = true;
      state.getDraftMessageError = "";
    },
    getDraftMessageSuccess: (state, { payload }) => {
      const response = JSON.parse(payload);
      state.getDraftMessageError = "";
      state.getDraftMessageLoading = false;
      state.draftMessage = response;
    },
    getDraftMessageFailed: (state, { payload }) => {
      state.getDraftMessageLoading = false;
      state.getDraftMessageError = payload[0]?.response?.body?.Message;
    },
    sendDraftMessage: (state, { payload }) => {
      state.sendDraftMessageLoading = true;
      state.sendDraftMessageError = "";
    },
    sendDraftMessageSuccess: (state, { payload }) => {
      state.sendDraftMessageSuccess = true;
      state.sendDraftMessageError = "";
      state.sendDraftMessageLoading = false;
      state.draftMessage = {};
    },
    sendDraftMessageFailed: (state, { payload }) => {
      state.sendDraftMessageSuccess = false;
      state.sendDraftMessageLoading = false;
      state.sendDraftMessageError = payload[0]?.response?.body?.Message;
    },
    sendDraftMessageSilent: (state, { payload }) => {
      // Silent autosave - no loading state changes to avoid UI interference
      // This action will be handled by the same cycle as sendDraftMessage
      // but won't show loading indicators or success messages
    },
    sendDraftMessageSilentSuccess: (state, { payload }) => {
      // Silent success - update draft message but no user feedback
      // Preserve the original MSG_Guid if the response contains a zero GUID
      if (payload) {
        const newDraftMessage = JSON.parse(payload);
        const currentMsgGuid = state.draftMessage.MSG_Guid;

        // If the new MSG_Guid is zero GUID and we have a valid current one, preserve it
        if (newDraftMessage.MSG_Guid === "00000000-0000-0000-0000-000000000000" &&
            currentMsgGuid &&
            currentMsgGuid !== "00000000-0000-0000-0000-000000000000") {
          newDraftMessage.MSG_Guid = currentMsgGuid;
        }

        state.draftMessage = newDraftMessage;
      } else {
        state.draftMessage = {};
      }
    },
    sendDraftMessageSilentFailed: (state, { payload }) => {
      // Silent failure - no user feedback for autosave failures
      // Could log for debugging purposes but don't show errors to user
    },
    clearInboxSearchFields(state) {
      state.inboxSearchFields = INBOX_SEARCH_FIELD_INITIAL_VALUE;
    },
    postMessageComment: (state, { payload }) => {
      state.postMessageCommentLoading = true;
      state.postMessageCommentError = "";
    },
    postMemoComment: (state, { payload }) => {
      state.postMessageCommentLoading = true;
      state.postMessageCommentError = "";
    },
    postMessageCommentSuccess: (state, { payload }) => {
      state.postMessageCommentLoading = false;
      state.postMessageCommentError = "";
      state.postMessageCommentSuccess = true;
      state.umsGuidToSendComment = "";
    },
    postMessageCommentSuccessDismiss: (state) => {
      state.postMessageCommentSuccess = false;
    },
    postMessageCommentFailed: (state, { payload }) => {
      state.postMessageCommentLoading = false;
      state.postMessageCommentError = payload[0]?.response?.body?.Message;
    },
    getMessageCommentUsers: (state, { payload }) => {
      state.getMessageCommentUsersLoading = true;
      state.getMessageCommentUsersError = "";
    },
    getMessageCommentUsersSuccess: (state, { payload }) => {
      const UMS_Guid = payload.UMS_Guid;
      const response: RelatedUsersListDTO = JSON.parse(payload.response);

      const mapUser = (user: RelatedUsersDTO, index: number) => {
        return {
          id: user.USR_Guid,
          value: user.USR_UserName,
          avatarName: user.USR_UserAbbreviation.charAt(0).toUpperCase(), // First letter of abbreviation
          avatarBackgroundColor: index % 2 === 0 ? "#FFB7CD" : "#FF8833", // Even index → #FFB7CD, Odd index → #FF8833
        };
      };
      state.commentMessageUsers = response.map((user, index) =>
        mapUser(user, index)
      );

      state.getMessageCommentUsersLoading = false;
      state.getMessageCommentUsersError = "";
    },
    getMessageCommentUsersFailed: (state, { payload }) => {
      state.getMessageCommentUsersLoading = false;
      state.getMessageCommentUsersError = payload[0]?.response?.body?.Message;
    },
    starMessageComment: (state, { payload }) => {
      state.starMessageCommentLoading = true;
      state.starMessageCommentUsersError = "";
    },
    starMessageCommentSuccess: (state, { payload }) => {
      state.starMessageCommentLoading = false;
      state.starMessageCommentUsersError = "";
    },
    starMessageCommentFailed: (state, { payload }) => {
      state.starMessageCommentLoading = false;
      state.starMessageCommentUsersError = payload[0]?.response?.body?.Message;
    },
    uploadAttachment: (state, { payload }) => {
      state.uploadAttachmentLoading = true;
      state.uploadAttachmentError = "";
    },
    uploadAttachmentSuccess: (state, { payload }) => {
      state.uploadAttachmentLoading = false;
      state.uploadAttachmentError = "";
    },
    uploadAttachmentFailed: (state, { payload }) => {
      state.uploadAttachmentLoading = false;
      state.uploadAttachmentError = payload[0]?.response?.body?.Message;
    },
  },
});

// Export the action creators for dispatching
export const {
  clearFolders,
  addFolders,
  removeFolder,
  setDefaultFolder,
  archiveToFolder,
  setFromSettings,
  setFromArchiveMessage,
  updateFolderMessages,
  setBottomSheetMenuState,
  longTapToSelectEmail,
  tapToSelectAdditionalEmail,
  deselectEmail,
  cancelMultiSelection,
  openMoreActionsDropdown,
  linkEmailsToCase,
  selectAllEmails,
  unSelectAllEmails,
  clearAllSelections,
  scrollToHideHeader,
  scrollToRevealHeader,
  loadMoreEmailsOnScroll,
  sendMessage,
  sendMessageSuccess,
  sendMessageFailed,
  sendMessageClearStatus,
  getDraftMessage,
  getDraftMessageSuccess,
  getDraftMessageFailed,
  sendDraftMessage,
  sendDraftMessageSuccess,
  sendDraftMessageFailed,
  sendDraftMessageSilent,
  sendDraftMessageSilentSuccess,
  sendDraftMessageSilentFailed,
  clearInboxSearchFields,
  postMessageComment,
  postMessageCommentSuccess,
  postMessageCommentFailed,
  getMessageCommentUsers,
  getMessageCommentUsersFailed,
  getMessageCommentUsersSuccess,
  postMessageCommentSuccessDismiss,
  starMessageComment,
  starMessageCommentSuccess,
  starMessageCommentFailed,
  uploadAttachment,
  uploadAttachmentSuccess,
  uploadAttachmentFailed,
  postMemoComment,
} = generalSlice.actions;

export default generalSlice.reducer;
