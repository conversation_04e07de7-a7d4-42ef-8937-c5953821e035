import { createSlice } from "@reduxjs/toolkit";
import { CommentListDTO } from "../types/DTOs/CommentDTO";
import { CaseDTO } from "../types/DTOs/CasesListDTO";
import { CaseDetailsDTO } from "../types/DTOs/CaseDetailsDTO";
import { mapCaseDetails } from "../helpers/mapCase";
import { AttachmentDTO } from "../types/DTOs/AttachmentDTO";
import { CaseMessageDTO } from "../types/DTOs/caseMessageDTO";
import { TaskDTO } from "../types/DTOs/taskDΤΟ";
import { WorkDTO, WorkListDTO } from "../types/DTOs/workDΤΟ";

// Initial state with Messages and Folders structures
const initialState = {
  casesListCount: 0,
  casesList: {
    byId: {},
    allIds: [],
  },
  caseDetails: {
    byId: {},
    allIds: [] as string[],
  },

  tasks: {
    byId: {},
    allIds: [],
  },

  works: {
    byId: {},
    allIds: [],
  },

  casesListLoading: false,
  casesListLoadingMore: false,
  casesListError: null,

  getCaseCommentsLoading: false,
  getCaseCommentsError: "",

  caseDetailsLoading: false,
  caseDetailsError: "",

  uploadAttachmentLoading: false,
  uploadAttachmentError: "",
  appliedSearchFilters: [],

  caseMessagesLoading: false,
  caseMessagesError: "",

  caseLinkedCasesLoading: false,
  caseLinkedCasesError: "",

  caseTasksLoading: false,
  caseTasksError: "",

  taskWorksLoading: false,
  taskWorksError: "",
};

const casesSlice = createSlice({
  name: "casesList",
  initialState,
  reducers: {
    fetchGridCases(state, { payload }) {
      if (payload?.skipFirst) {
        state.casesListLoadingMore = true;
      } else {
        state.casesListLoading = true;
      }
    },
    fetchGridCasesSuccess(state, action) {
      const { folderGuid, responseBody, loadMore, searchFilters } =
        action.payload;

      const newIds: string[] = responseBody.value.map(
        (message: CaseDTO) => message.CAS_Guid
      );

      // Update casesList.byId without overwriting existing data
      responseBody.value.forEach((message: CaseDTO) => {
        const { CAS_Guid } = message;
        state.casesList.byId[CAS_Guid] = message;
      });

      // Prepend newIds to state.casesList.allIds
      state.casesList.allIds = Array.from(
        new Set([...newIds, ...state.casesList.allIds])
      );

      // Ensure gridCaseCount is updated correctly from response
      state.casesListCount = parseInt(responseBody["odata.count"], 10) || 0;
      state.casesListLoading = false;
      state.casesListLoadingMore = false;
      state.casesListError = null;
      state.appliedSearchFilters = searchFilters;
    },
    fetchGridCasesFailed(state, { payload }) {
      state.casesListError =
        payload[0]?.response?.body["odata.error"]?.message?.value ||
        "Error fetching inbox items";
      state.casesListLoading = false;
      state.casesListLoadingMore = false;
    },
    loadMoreCasesOnScroll(state) {
      // Placeholder for loading more emails when scrolling down
    },
    getCaseDetails: (state, action) => {
      state.caseDetailsLoading = true;
      state.caseDetailsError = "";
    },
    getCaseDetailsSuccess: (state, { payload }) => {
      const response: CaseDetailsDTO = payload.responseBody;
      const caseDetails = state.caseDetails.byId[response.CAS_Guid];

      state.caseDetails.byId[response.CAS_Guid] = mapCaseDetails(
        response,
        caseDetails
      );
      state.caseDetails.allIds.indexOf(response.CAS_Guid) === -1
        ? state.caseDetails.allIds.push(response.CAS_Guid)
        : state.caseDetails.allIds;
      state.caseDetailsLoading = false;
    },
    getCaseDetailsFailed: (state, { payload }) => {
      state.caseDetailsError = payload[0].response?.Message;
      state.caseDetailsLoading = false;
    },
    starCase: (state) => { },
    starCaseSuccess: (state, action) => { },
    starCaseFailed: (state, action) => { },
    getCaseComments: (state, action) => {
      state.getCaseCommentsLoading = true;
    },
    getCaseCommentsSuccess: (state, { payload }) => {
      const response: CommentListDTO = JSON.parse(payload.messageBody);
      const caseDetails = state.caseDetails.byId[payload.guid];

      state.getCaseCommentsError = "";

      if (caseDetails) {
        caseDetails.commentIds = response?.map((comment) => comment.CMM_Guid);
      }

      state.getCaseCommentsLoading = false;
    },
    getCaseCommentsFailed: (state, { payload }) => {
      state.getCaseCommentsLoading = false;
      state.getCaseCommentsError = payload[0].response?.Message;
    },
    getCaseAttachmentsSuccess: (state, { payload }) => {
      const response: AttachmentDTO[] = JSON.parse(payload.response);

      if (state.caseDetails.byId[payload.guid]) {
        state.caseDetails.byId[payload.guid].attachmentIds = response.map(
          (attachment) => attachment.FLN_Guid
        );
      }
    },
    getCaseMessages: (state, { payload }) => {
      state.caseMessagesError = "";
      state.caseMessagesLoading = true;
    },
    getCaseMessagesSuccess: (state, { payload }) => {
      const { responseBody, caseId } = (payload || {}) as {
        responseBody?: CaseMessageDTO[];
        caseId: string;
      };

      if (state.caseDetails.byId[caseId]) {
        state.caseDetails.byId[caseId].messageIds = responseBody?.map(
          (message) => message.UMS_Guid
        );
      }

      state.caseMessagesLoading = false;
    },
    getCaseMessagesFailed: (state, { payload }) => {
      state.caseMessagesError = payload[0].response?.Message;
      state.caseMessagesLoading = false;
    },
    getCaseLinkedCases: (state, { payload }) => {
      state.caseLinkedCasesError = "";
      state.caseLinkedCasesLoading = true;
    },
    getCaseLinkedCasesSuccess: (state, { payload }) => {
      const { responseBody = [], caseId } = payload;

      const linkedCasesIds: string[] = [];

      const newLinkedCasesById = responseBody.reduce((acc, linkedCase) => {
        linkedCasesIds.push(linkedCase.CAS_Guid);
        acc[linkedCase.CAS_Guid] = {
          ...(state.casesList.byId?.[linkedCase.CAS_Guid] || {}),
          CAS_Guid: linkedCase.CAS_Guid,
          CAS_Reference: linkedCase.CAS_Reference,
          CAS_Title: linkedCase.CAS_Title,
          CAS_CreatedTimestamp: linkedCase.CreatedTimestamp,
          CreatedUserName: linkedCase.CreatedUserName,
        };
        return acc;
      }, {} as Record<string, CaseDTO>);

      if (!state.caseDetails.byId[caseId]) {
        state.caseDetails.byId[caseId] = {} as CaseDetailsDTO; // or skip setting linkedCasesIds entirely
      }

      state.caseDetails.byId[caseId].linkedCasesIds = linkedCasesIds;
      state.casesList.byId = { ...state.casesList.byId, ...newLinkedCasesById };
      state.caseLinkedCasesLoading = false;
    },
    getCaseLinkedCasesFailed: (state, { payload }) => {
      state.caseLinkedCasesError = payload[0].response?.Message;
      state.caseLinkedCasesLoading = false;
    },
    getCaseTasks: (state, { payload }) => {
      state.caseTasksError = "";
      state.caseTasksLoading = true;
    },
    getCaseTasksSuccess: (state, { payload }) => {
      const { responseBody = [], caseId } = payload as {
        responseBody?: TaskDTO[];
        caseId: string;
      };

      const taskIds: string[] = [];

      const newTasksById = responseBody.reduce((acc, task) => {
        taskIds.push(task.CAS_Guid);

        acc[task.CAS_Guid] = {
          ...(state.tasks.byId?.[task.CAS_Guid] || {}),
          ...task,
        };
        return acc;
      }, {} as Record<string, TaskDTO>);

      if (state.caseDetails.byId[caseId]) {
        state.caseDetails.byId[caseId].taskIds = taskIds;
      }

      state.tasks.byId = { ...state.tasks.byId, ...newTasksById };
      state.caseTasksLoading = false;
    },
    getCaseTasksFailed: (state, { payload }) => {
      state.caseTasksError = payload[0].response?.Message;
      state.caseTasksLoading = false;
    },
    getTaskWorks: (state, { payload }) => {
      state.taskWorksError = "";
      state.taskWorksLoading = true;
    },
    getTaskWorksSuccess: (state, { payload }) => {
      const { responseBody, taskId } = payload as {
        responseBody?: WorkListDTO;
        taskId: string;
      };
      const { value } = responseBody || {};

      const workIds: string[] = [];
      const newWorksById = value.reduce((acc, work) => {
        workIds.push(work.TWK_Guid);

        acc[work.TWK_Guid] = {
          ...(state.works.byId?.[work.TWK_Guid] || {}),
          ...work
        };
        return acc;
      }, {} as Record<string, WorkDTO>);

      if (state.tasks.byId[taskId]) {
        state.tasks.byId[taskId].workIds = workIds;
      }

      state.works.byId = { ...state.works.byId, ...newWorksById };
      state.taskWorksLoading = false;
    },
    getTaskWorksFailed: (state, { payload }) => {
      state.taskWorksError = payload[0].response?.Message;
      state.taskWorksLoading = false;
    },
  },
});

// Export the action creators for dispatching
export const {
  fetchGridCases,
  fetchGridCasesSuccess,
  fetchGridCasesFailed,
  loadMoreCasesOnScroll,
  getCaseDetails,
  getCaseDetailsSuccess,
  getCaseDetailsFailed,
  starCase,
  starCaseSuccess,
  starCaseFailed,
  getCaseComments,
  getCaseCommentsSuccess,
  getCaseCommentsFailed,
  getCaseAttachmentsSuccess,
  getCaseMessages,
  getCaseMessagesSuccess,
  getCaseMessagesFailed,
  getCaseLinkedCases,
  getCaseLinkedCasesSuccess,
  getCaseLinkedCasesFailed,
  getCaseTasks,
  getCaseTasksSuccess,
  getCaseTasksFailed,
  getTaskWorks,
  getTaskWorksSuccess,
  getTaskWorksFailed,
} = casesSlice.actions;

export default casesSlice.reducer;
