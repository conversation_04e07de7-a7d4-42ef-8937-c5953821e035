import React, { RefObject, useMemo } from "react";
import {
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  SearchInput,
  SPACING,
  useThemeAwareObject,
  AdvancedSearchButtons,
} from "b-ui-lib";
import { SCREEN_NAMES } from "../../constants/screenNames";
import { TEST_IDS } from "../../constants/testIds";
import filterSearchSuggestions from "../../helpers/filterSearchSuggestions";
import {
  INBOX_SEARCH_FIELD_INITIAL_VALUE,
  SEARCH_FIELD_NAMES,
} from "../../constants/searchFieldsMessage";
import { InboxSearchFields } from "../../components/inboxSearchFilters/InboxSearchFiltersScreen";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  inboxSearchFields: InboxSearchFields;
  appliedSearchFiltersCount?: number;
  clearInboxSearchFields: () => void;
  setInboxSearchFields: (fields: {}) => void;
  fetchGridMessages: () => void;
  searchSuggestions: string[];
  addSearchSuggestion: (suggestion: string) => void;
  searchInputRef: RefObject<TextInput>;
  isQuickSearchEnabled?: boolean;
  setIsQuickSearchEnabledAction: (isEnabled: boolean) => void;
};

const InboxSearchHeader = ({
  navigation,
  inboxSearchFields,
  appliedSearchFiltersCount,
  clearInboxSearchFields,
  setInboxSearchFields,
  fetchGridMessages,
  searchSuggestions,
  addSearchSuggestion,
  searchInputRef,
  isQuickSearchEnabled,
  setIsQuickSearchEnabledAction,
}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  const navigateToInboxSearchFilters = () =>
    navigation.navigate(SCREEN_NAMES.inboxSearchFilters);

  const handleFetchGridMessages = (latestSearchText: string) => {
    if (!latestSearchText || latestSearchText?.trim() === "") {
      return;
    }

    fetchGridMessages();
    addSearchSuggestion(latestSearchText);
  };

  const handleSearchInputChange = (inputValue: string) => {
    if (!inputValue || inputValue === "") {
      return clearInboxSearchFields();
    }

    // When searching from search input we want to initialize filters.
    setInboxSearchFields({
      ...INBOX_SEARCH_FIELD_INITIAL_VALUE,
      [SEARCH_FIELD_NAMES.searchText]: inputValue,
    });

    if (!isQuickSearchEnabled) {
      setIsQuickSearchEnabledAction(true);
    }
  };

  const filteredSuggestions = useMemo(
    () =>
      filterSearchSuggestions(searchSuggestions, inboxSearchFields.searchText),
    [searchSuggestions, inboxSearchFields]
  );

  const handleSearchInputClear = () => {
    clearInboxSearchFields();
    fetchGridMessages();
  };

  const handleSuggestionPress = (suggestion: string) => {
    setInboxSearchFields({
      [SEARCH_FIELD_NAMES.searchText]: suggestion,
    });

    handleFetchGridMessages(suggestion);
  };

  const onOutsidePress = () => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
  };

  return (
    <TouchableWithoutFeedback onPress={onOutsidePress}>
      <View style={styles.container}>
        <SearchInput
          testID={TEST_IDS.inboxSearchInput}
          searchInputRef={searchInputRef}
          value={inboxSearchFields?.searchText}
          containerStyle={styles.searchInput}
          onChangeText={handleSearchInputChange}
          handleDebounceFunction={handleFetchGridMessages}
          handleInputClear={handleSearchInputClear}
          placeholder={"Search here"}
          suggestions={filteredSuggestions}
          handleSuggestionPress={handleSuggestionPress}
        />

        <AdvancedSearchButtons
          searchFiltersCount={
            !isQuickSearchEnabled && appliedSearchFiltersCount > 0
              ? appliedSearchFiltersCount
              : 0
          }
          isClearAllButtonVisible={
            !isQuickSearchEnabled && appliedSearchFiltersCount > 0
          }
          handleClearAll={clearInboxSearchFields}
          handlePressCriteriaButton={navigateToInboxSearchFilters}
          style={{ buttonsContainer: styles.buttonsHeader }}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default InboxSearchHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: SPACING.S,
    },
    headerTitle: {
      fontSize: 20,
    },
    multiSelectIconsContainer: {
      flexDirection: "row",
      gap: SPACING.TEN,
      alignItems: "center",
      paddingLeft: SPACING.XS,
    },
    searchInput: {
      marginVertical: SPACING.S,
      marginHorizontal: SPACING.M,
      zIndex: 1000,
    },
    buttonsHeader: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};
