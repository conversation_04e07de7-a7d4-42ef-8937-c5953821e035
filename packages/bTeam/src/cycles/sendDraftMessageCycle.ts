import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  sendDraftMessage,
  sendDraftMessageFailed,
  sendDraftMessageSuccess,
  sendDraftMessageSilent,
  sendDraftMessageSilentFailed,
  sendDraftMessageSilentSuccess
} from "../slices/generalSlice.ts";
import { fetchGridMessages } from "../slices/gridMessageSlice.ts";

export const putDraftMessage = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === sendDraftMessage.type || action.type === sendDraftMessageSilent.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      const { MSG_From, MSG_To, MSG_Subject, MSG_Body, MSG_Cc
        , MSG_Bcc, UNI_Guid, UMS_Guid, AttachmentsInitial, AttachmentsAdded, AttachmentsDeleted, AttachmentsGuids, MSG_AttachmentsGuid, MessageSend } = action?.payload

      return {
        url: `${domainBaseUrl}/api/SendMessages2/${UMS_Guid}`,
        category: action.type === sendDraftMessageSilent.type ? "putDraftMessageSilent" : "putDraftMessage",
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        send: {
          UMS_Guid,
          UNI_Guid,
          MSG_MST_Id: 1,
          MSG_From,
          MSG_To,
          MSG_Cc,
          MSG_Bcc,
          MSG_Subject,
          MSG_IsHtml: 1,
          MSG_Body,
          MSG_Importance: 2,
          MSG_AttachmentsCount: AttachmentsGuids?.length || 0,
          MSG_AttachmentsGuid,
          AttachmentsGuids,
          AttachmentsInitial,
          AttachmentsAdded,
          AttachmentsDeleted,
          MSG_RequestDeliveryReceipt: 0,
          MSG_RequestReadReceipt: 0,
          MSG_SendMailingLists: "",
          MSG_MessageActionType: 12,
          MessageSend,
          MSG_ClientApplication: 2,
          UMS_Guid_Parent: null,
        },
      };
    });

  const response$ = sources.HTTP.select("putDraftMessage")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 201);

  const action1$ = xs
    .combine(response$)
    .map(([response]) => {
      const responseBody = (response as any)?.text;

      return sendDraftMessageSuccess(responseBody);
    });

  const action2$ = xs
    .combine(response$)
    .map(([response]) => {

      return fetchGridMessages({});
    })

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const putDraftMessageFailed = (sources) => {
  const response$ = sources.HTTP.select("putDraftMessage")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 201);

  const action$ = xs
    .combine(response$)
    .map((arr) => sendDraftMessageFailed(arr));

  return {
    ACTION: action$,
  };
};

// Silent autosave success cycle
export const putDraftMessageSilentSuccess = (sources) => {
  const response$ = sources.HTTP.select("putDraftMessageSilent")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 201);

  const action$ = xs
    .combine(response$)
    .map(([response]) => {
      const responseBody = (response as any)?.text;
      return sendDraftMessageSilentSuccess(responseBody);
    });

  return {
    ACTION: action$,
  };
};

// Silent autosave failure cycle
export const putDraftMessageSilentFailed = (sources) => {
  const response$ = sources.HTTP.select("putDraftMessageSilent")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 201);

  const action$ = xs
    .combine(response$)
    .map((arr) => sendDraftMessageSilentFailed(arr));

  return {
    ACTION: action$,
  };
};
